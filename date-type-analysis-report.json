{"timestamp": "2025-08-04T22:24:55.205Z", "summary": {"totalIssues": 539, "highSeverityCount": 269, "mediumSeverityCount": 261, "lowSeverityCount": 9, "issueTypes": {"TO_ISO_STRING_USAGE": 168, "NEW_DATE_TO_ISO": 129, "STRING_DATE_TYPE": 140, "LOCAL_DATE_FORMAT": 70, "MANUAL_DATE_FORMAT": 23, "JSON_STRINGIFY_DATE": 9}, "fileCount": 81}, "details": {"lib\\database-config.ts": [{"type": "TO_ISO_STRING_USAGE", "line": 129, "content": "timestamp: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 129, "content": "timestamp: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}], "lib\\date-utils.ts": [{"type": "STRING_DATE_TYPE", "line": 204, "content": "export function formatDateForCSV(date: Date | string | null | undefined): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 214, "content": "export function getRelativeTimeArabic(date: Date | string | null | undefined): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 249, "content": "export function getCurrentArabicDate(): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 258, "content": "export function parseArabicDate(input: string | Date | null | undefined): Date {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 269, "content": "export function formatArabicDate(date: Date | string): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 277, "content": "export function formatArabicDateOnly(date: Date | string): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 285, "content": "export function formatArabicTime(date: Date | string): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 293, "content": "export function formatShortDate(date: Date | string): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "lib\\device-tracking-utils.ts": [{"type": "STRING_DATE_TYPE", "line": 17, "content": "date: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 21, "content": "expiryDate: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 35, "content": "date: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "lib\\export-utils\\canvas-pdf-enhanced.ts": [{"type": "LOCAL_DATE_FORMAT", "line": 305, "content": "const arabicDate = currentDate.toLocaleDateString('ar-EG');", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 306, "content": "const englishDate = currentDate.toLocaleDateString('en-US');", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 425, "content": "const saleDate = lastSale.date ? new Date(lastSale.date).toLocaleDateString('ar-EG') : '-';", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 565, "content": "const eventDate = event.formattedDate || new Date(event.date).toLocaleDateString('ar-EG');", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 685, "content": "const arabicDate = currentDate.toLocaleDateString('ar-EG');", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 686, "content": "const englishDate = currentDate.toLocaleDateString('en-US');", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "lib\\export-utils\\enhanced-html-export.ts": [{"type": "STRING_DATE_TYPE", "line": 67, "content": "date: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 71, "content": "expiryDate: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 81, "content": "date: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 83, "content": "formattedDate?: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 923, "content": "arabicDate: string,", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 924, "content": "englishDate: string,", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 925, "content": "time: string", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 1082, "content": "function generateTimelineSection(timelineEvents: TimelineEvent[], isCustomerView: boolean, language: string): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 1124, "content": "arabicDate: string,", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 1125, "content": "englishDate: string,", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 1126, "content": "time: string,", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "lib\\network.ts": [{"type": "STRING_DATE_TYPE", "line": 80, "content": "export function validateIpAddress(ip: string): boolean {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 102, "content": "export function formatConnectionTime(date: Date): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "lib\\notification-service.ts": [{"type": "STRING_DATE_TYPE", "line": 111, "content": "static getOverdueTimeText(priority: string): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 135, "content": "static getTimeText(minutes: number): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "lib\\print-templates\\index.ts": [{"type": "STRING_DATE_TYPE", "line": 321, "content": "formatDate: (date: string | Date): string => {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "LOCAL_DATE_FORMAT", "line": 327, "content": "return `${amount.toLocaleString('ar-EG')} ${currency}`;", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "lib\\search-service.ts": [{"type": "STRING_DATE_TYPE", "line": 8, "content": "dateFrom?: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 9, "content": "dateTo?: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 22, "content": "requestDate: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "lib\\template-service.ts": [{"type": "STRING_DATE_TYPE", "line": 22, "content": "createdAt: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 23, "content": "updatedAt: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "lib\\types.ts": [{"type": "STRING_DATE_TYPE", "line": 34, "content": "dateAdded: string; // ISO date string", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 41, "content": "replacementDate?: string; // تاريخ الاستبدال", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 67, "content": "createdAt?: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 74, "content": "date: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 81, "content": "createdAt: string; // ← إضافة الختم الزمني", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 94, "content": "createdAt?: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 101, "content": "date: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 110, "content": "processedDate?: string; // تاريخ المعالجة", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 112, "content": "createdAt: string; // ← الختم الزمني لإنشاء المرتجع", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 120, "content": "returnDate: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 174, "content": "createdAt?: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 182, "content": "supplyDate: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 189, "content": "createdAt: string; // ISO timestamp for when the order was created", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 205, "content": "date: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 254, "content": "createdAt?: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 261, "content": "date: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 264, "content": "createdAt: string; // ← إضافة الختم الزمني", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 270, "content": "repairDate: string; // Date sent from maintenance", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 274, "content": "acknowledgedDate?: string; // Date received by warehouse", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 286, "content": "createdAt?: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 293, "content": "date: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 302, "content": "createdAt: string; // ← إضافة الختم الزمني", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 314, "content": "createdAt?: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 321, "content": "date: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 329, "content": "createdAt: string; // ← إضافة الختم الزمني", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 342, "content": "createdAt?: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 350, "content": "date: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 357, "content": "createdAt: string; // ← إضافة الختم الزمني", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 370, "content": "date: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 403, "content": "requestDate: string; // ISO string", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 413, "content": "resolutionDate?: string; // ISO string", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 531, "content": "sentDate: string; // ISO", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 557, "content": "soldDate?: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 575, "content": "id: string; // Changed to string to accommodate STK- format", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 579, "content": "startTime: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 580, "content": "endTime: string | null;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 587, "content": "scheduledDate?: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 650, "content": "createdAt: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 682, "content": "dateFrom?: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 683, "content": "dateTo?: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "app\\(main)\\accept-devices\\page.tsx": [{"type": "TO_ISO_STRING_USAGE", "line": 123, "content": "date: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 123, "content": "date: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "LOCAL_DATE_FORMAT", "line": 156, "content": "doc.text(`التاريخ: ${new Date(order.date).toLocaleDateString()}`, 10, 47);", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 430, "content": "{new Date(order.date).toLocaleDateString()}", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 476, "content": "{new Date(viewingOrder.date).toLocaleDateString()}", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "app\\(main)\\audit-logs\\page.tsx": [{"type": "LOCAL_DATE_FORMAT", "line": 43, "content": "<TableCell>{new Date(log.timestamp).toLocaleString()}</TableCell>", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "app\\(main)\\grading\\2page.tsx": [{"type": "STRING_DATE_TYPE", "line": 153, "content": "const formatDateTime = (dateTimeString: string): string => {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "MANUAL_DATE_FORMAT", "line": 160, "content": "const year = date.getFullYear();", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "MANUAL_DATE_FORMAT", "line": 161, "content": "const month = String(date.getMonth() + 1).padStart(2, '0');", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "MANUAL_DATE_FORMAT", "line": 162, "content": "const day = String(date.getDate()).padStart(2, '0');", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "STRING_DATE_TYPE", "line": 230, "content": "const validateDeviceData = (deviceId: string, currentItems: EvaluatedDevice[]) => {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "TO_ISO_STRING_USAGE", "line": 468, "content": "date: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 468, "content": "date: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 503, "content": "createdAt: new Date().toISOString()", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 503, "content": "createdAt: new Date().toISOString()", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "LOCAL_DATE_FORMAT", "line": 696, "content": "doc.text(`التاريخ: ${new Date().toLocaleDateString('ar-EG')}`, 190, 59, {", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "TO_ISO_STRING_USAGE", "line": 921, "content": "defaultValue={new Date().toISOString().slice(0, 16)}", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 921, "content": "defaultValue={new Date().toISOString().slice(0, 16)}", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "LOCAL_DATE_FORMAT", "line": 1049, "content": "{new Date(order.date).toLocaleDateString('ar-EG')}", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 1757, "content": "const saveTime = draftData.timestamp ? new Date(draftData.timestamp).toLocaleString('ar-EG') : 'غير معروف';", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "app\\(main)\\grading\\page.tsx": [{"type": "TO_ISO_STRING_USAGE", "line": 177, "content": "timestamp: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 177, "content": "timestamp: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "LOCAL_DATE_FORMAT", "line": 226, "content": "description: `تم تحميل المسودة المحفوظة بتاريخ ${new Date(draftData.timestamp).toLocaleString('ar-EG')}`,", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "STRING_DATE_TYPE", "line": 253, "content": "const formatDateTime = (dateTimeString: string): string => {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "MANUAL_DATE_FORMAT", "line": 260, "content": "const year = date.getFullYear();", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "MANUAL_DATE_FORMAT", "line": 261, "content": "const month = String(date.getMonth() + 1).padStart(2, '0');", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "MANUAL_DATE_FORMAT", "line": 262, "content": "const day = String(date.getDate()).padStart(2, '0');", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "TO_ISO_STRING_USAGE", "line": 590, "content": "date: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 590, "content": "date: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "LOCAL_DATE_FORMAT", "line": 739, "content": "doc.text(`التاريخ: ${new Date().toLocaleDateString('ar-EG')}`, 190, 59, {", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "TO_ISO_STRING_USAGE", "line": 923, "content": "defaultValue={new Date().toISOString().slice(0, 16)}", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 923, "content": "defaultValue={new Date().toISOString().slice(0, 16)}", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "LOCAL_DATE_FORMAT", "line": 1051, "content": "{new Date(order.date).toLocaleDateString('ar-EG')}", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "app\\(main)\\inventory\\page.tsx": [{"type": "TO_ISO_STRING_USAGE", "line": 724, "content": "doc.save(`inventory_report_${new Date().toISOString().slice(0, 10)}.pdf`);", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 724, "content": "doc.save(`inventory_report_${new Date().toISOString().slice(0, 10)}.pdf`);", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 741, "content": "`inventory_report_${new Date().toISOString().slice(0, 10)}.xlsx`", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 741, "content": "`inventory_report_${new Date().toISOString().slice(0, 10)}.xlsx`", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1818, "content": "doc.save(`${deviceDetails?.title.replace(/\\s+/g, '_')}_${new Date().toISOString().slice(0, 10)}.pdf`);", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1818, "content": "doc.save(`${deviceDetails?.title.replace(/\\s+/g, '_')}_${new Date().toISOString().slice(0, 10)}.pdf`);", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1852, "content": "XLSX.writeFile(workbook, `${deviceDetails?.title.replace(/\\s+/g, '_')}_${new Date().toISOString().slice(0, 10)}.xlsx`);", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1852, "content": "XLSX.writeFile(workbook, `${deviceDetails?.title.replace(/\\s+/g, '_')}_${new Date().toISOString().slice(0, 10)}.xlsx`);", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}], "app\\(main)\\inventory\\page_backup.tsx": [{"type": "TO_ISO_STRING_USAGE", "line": 372, "content": "doc.save(`inventory_report_${new Date().toISOString().slice(0, 10)}.pdf`);", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 372, "content": "doc.save(`inventory_report_${new Date().toISOString().slice(0, 10)}.pdf`);", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 389, "content": "`inventory_report_${new Date().toISOString().slice(0, 10)}.xlsx`", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 389, "content": "`inventory_report_${new Date().toISOString().slice(0, 10)}.xlsx`", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 931, "content": "doc.save(`all_devices_report_${new Date().toISOString().slice(0, 10)}.pdf`);", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 931, "content": "doc.save(`all_devices_report_${new Date().toISOString().slice(0, 10)}.pdf`);", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 952, "content": "`all_devices_report_${new Date().toISOString().slice(0, 10)}.xlsx`", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 952, "content": "`all_devices_report_${new Date().toISOString().slice(0, 10)}.xlsx`", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}], "app\\(main)\\layout.tsx": [{"type": "LOCAL_DATE_FORMAT", "line": 437, "content": "{employee.lastActivity.toLocaleString('ar-EG')}", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "app\\(main)\\maintenance\\MaintenanceMemo.tsx": [{"type": "STRING_DATE_TYPE", "line": 12, "content": "maintenanceStartDate: string; // ISO string", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "app\\(main)\\maintenance\\page.tsx": [{"type": "STRING_DATE_TYPE", "line": 159, "content": "const formatDateTime = (dateTimeString: string): string => {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "MANUAL_DATE_FORMAT", "line": 165, "content": "const year = date.getFullYear();", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "MANUAL_DATE_FORMAT", "line": 166, "content": "const month = String(date.getMonth() + 1).padStart(2, '0');", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "MANUAL_DATE_FORMAT", "line": 167, "content": "const day = String(date.getDate()).padStart(2, '0');", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "TO_ISO_STRING_USAGE", "line": 195, "content": "new Date().toISOString().slice(0, 16), // YYYY-MM-DDTHH:MM", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 195, "content": "new Date().toISOString().slice(0, 16), // YYYY-MM-DDTHH:MM", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 226, "content": "new Date().toISOString().split('T')[0]", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 226, "content": "new Date().toISOString().split('T')[0]", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "STRING_DATE_TYPE", "line": 276, "content": "dateFrom: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 277, "content": "dateTo: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "TO_ISO_STRING_USAGE", "line": 458, "content": "timestamp: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 458, "content": "timestamp: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 475, "content": "setOrderDate(draft.orderDate || new Date().toISOString().slice(0, 16));", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 475, "content": "setOrderDate(draft.orderDate || new Date().toISOString().slice(0, 16));", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 535, "content": "setOrderDate(new Date().toISOString().slice(0, 16)); // YYYY-MM-DDTHH:MM", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 535, "content": "setOrderDate(new Date().toISOString().slice(0, 16)); // YYYY-MM-DDTHH:MM", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 562, "content": "timestamp: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 562, "content": "timestamp: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 580, "content": "setDeliveryOrderDate(draft.deliveryOrderDate || new Date().toISOString().slice(0, 16));", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 580, "content": "setDeliveryOrderDate(draft.deliveryOrderDate || new Date().toISOString().slice(0, 16));", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 646, "content": "setDeliveryOrderDate(new Date().toISOString().slice(0, 16)); // YYYY-MM-DDTHH:MM", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 646, "content": "setDeliveryOrderDate(new Date().toISOString().slice(0, 16)); // YYYY-MM-DDTHH:MM", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 902, "content": "date: new Date(orderDate).toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 909, "content": "createdAt: new Date().toISOString(), // إضافة الختم الزمني", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 909, "content": "createdAt: new Date().toISOString(), // إضافة الختم الزمني", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 969, "content": "setOrderDate(new Date(order.date).toISOString().slice(0, 16)); // YYYY-MM-DDTHH:MM", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 985, "content": "uploadedAt: order.createdAt || new Date().toISOString()", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 985, "content": "uploadedAt: order.createdAt || new Date().toISOString()", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1243, "content": "date: new Date(deliveryOrderDate).toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1318, "content": "setDeliveryOrderDate(new Date(order.date).toISOString().slice(0, 16)); // YYYY-MM-DDTHH:MM", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1340, "content": "uploadedAt: order.createdAt || new Date().toISOString()", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1340, "content": "uploadedAt: order.createdAt || new Date().toISOString()", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "LOCAL_DATE_FORMAT", "line": 1680, "content": "التاريخ: ${new Date().toLocaleDateString('ar')}", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1872, "content": "maintenanceStartDate: new Date().toISOString(), // TODO: استخدم تاريخ البدء الفعلي", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1872, "content": "maintenanceStartDate: new Date().toISOString(), // TODO: استخدم تاريخ البدء الفعلي", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}], "app\\(main)\\maintenance-transfer\\page.tsx": [{"type": "TO_ISO_STRING_USAGE", "line": 113, "content": "date: new Date().toISOString().slice(0, 16), // YYYY-MM-DDTHH:MM", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 113, "content": "date: new Date().toISOString().slice(0, 16), // YYYY-MM-DDTHH:MM", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 310, "content": "date: new Date().toISOString().slice(0, 16), // YYYY-MM-DDTHH:MM", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 310, "content": "date: new Date().toISOString().slice(0, 16), // YYYY-MM-DDTHH:MM", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "STRING_DATE_TYPE", "line": 337, "content": "const formatDateTime = (dateTimeString: string): string => {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "MANUAL_DATE_FORMAT", "line": 344, "content": "const year = date.getFullYear();", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "MANUAL_DATE_FORMAT", "line": 345, "content": "const month = String(date.getMonth() + 1).padStart(2, '0');", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "MANUAL_DATE_FORMAT", "line": 346, "content": "const day = String(date.getDate()).padStart(2, '0');", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "TO_ISO_STRING_USAGE", "line": 404, "content": "date: new Date().toISOString().slice(0, 16) // تحديث التاريخ والوقت", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 404, "content": "date: new Date().toISOString().slice(0, 16) // تحديث التاريخ والوقت", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 428, "content": "date: new Date().toISOString().slice(0, 16), // YYYY-MM-DDTHH:MM", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 428, "content": "date: new Date().toISOString().slice(0, 16), // YYYY-MM-DDTHH:MM", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 461, "content": "date: new Date().toISOString().slice(0, 16)", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 461, "content": "date: new Date().toISOString().slice(0, 16)", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 484, "content": "timestamp: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 484, "content": "timestamp: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 513, "content": "date: draft.formState?.date || new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 513, "content": "date: draft.formState?.date || new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 620, "content": "timestamp: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 620, "content": "timestamp: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 649, "content": "date: draft.receiptFormState?.date || new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 649, "content": "date: draft.receiptFormState?.date || new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 672, "content": "date: new Date().toISOString().slice(0, 16),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 672, "content": "date: new Date().toISOString().slice(0, 16),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 820, "content": "uploadedAt: order.createdAt || new Date().toISOString()", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 820, "content": "uploadedAt: order.createdAt || new Date().toISOString()", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 837, "content": "uploadedAt: order.createdAt || new Date().toISOString()", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 837, "content": "uploadedAt: order.createdAt || new Date().toISOString()", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 920, "content": "date: new Date(formState.date).toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1236, "content": "date: new Date(order.date).toISOString().slice(0, 16), // YYYY-MM-DDTHH:MM", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1431, "content": "date: new Date(receiptFormState.date).toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1451, "content": "date: new Date(receiptFormState.date).toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1506, "content": "date: new Date(order.date).toISOString().slice(0, 16), // YYYY-MM-DDTHH:MM", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1533, "content": "dateAdded: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1533, "content": "dateAdded: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "LOCAL_DATE_FORMAT", "line": 3093, "content": "<TableCell>{item.price.toLocaleString()} ر.س</TableCell>", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "app\\(main)\\messaging\\page.tsx": [{"type": "STRING_DATE_TYPE", "line": 28, "content": "lastMessageDate: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "TO_ISO_STRING_USAGE", "line": 155, "content": "lastMessageDate: new Date(0).toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 319, "content": "sentDate: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 319, "content": "sentDate: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 484, "content": "lastMessageDate: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 484, "content": "lastMessageDate: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 527, "content": "lastMessageDate: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 527, "content": "lastMessageDate: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "STRING_DATE_TYPE", "line": 729, "content": "const forceUpdateConversation = (threadId: number, messageText: string) => {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "TO_ISO_STRING_USAGE", "line": 735, "content": "lastMessageDate: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 735, "content": "lastMessageDate: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}], "app\\(main)\\reports\\client-reports\\page.tsx": [{"type": "LOCAL_DATE_FORMAT", "line": 165, "content": "new Date(inv.date).toLocaleDateString('ar-EG'),", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 365, "content": "<TableCell>{new Date(invoice.date).toLocaleDateString('ar-EG')}</TableCell>", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "app\\(main)\\reports\\employee-reports\\page.tsx": [{"type": "TO_ISO_STRING_USAGE", "line": 108, "content": "doc.save(`employee_productivity_report_${new Date().toISOString().slice(0, 10)}.pdf`);", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 108, "content": "doc.save(`employee_productivity_report_${new Date().toISOString().slice(0, 10)}.pdf`);", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "LOCAL_DATE_FORMAT", "line": 148, "content": "{stat.user.lastLogin ? new Date(stat.user.lastLogin).toLocaleString('ar-EG') : 'لم يسجل دخول'}", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "app\\(main)\\reports\\grading-reports\\page.tsx": [{"type": "TO_ISO_STRING_USAGE", "line": 203, "content": "doc.save(`grading_report_${new Date().toISOString().slice(0, 10)}.pdf`);", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 203, "content": "doc.save(`grading_report_${new Date().toISOString().slice(0, 10)}.pdf`);", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}], "app\\(main)\\reports\\maintenance-reports\\page.tsx": [{"type": "MANUAL_DATE_FORMAT", "line": 88, "content": "toDate.setDate(toDate.getDate() + 1);", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 341, "content": "<TableCell>{new Date(log.repairDate).toLocaleDateString('ar-EG')}</TableCell>", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "app\\(main)\\reports\\supplier-reports\\page.tsx": [{"type": "STRING_DATE_TYPE", "line": 64, "content": "type AugmentedEvaluatedDevice = EvaluatedDevice & { supplyDate?: string };", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "LOCAL_DATE_FORMAT", "line": 562, "content": "{order.supplyOrderId} - {new Date(order.supplyDate).toLocaleDateString()}", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "app\\(main)\\requests\\page.tsx": [{"type": "STRING_DATE_TYPE", "line": 108, "content": "const updateRequestStatus = async (requestId: number, status: string, notes?: string) => {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "app\\(main)\\returns\\page.tsx": [{"type": "TO_ISO_STRING_USAGE", "line": 129, "content": "date: new Date().toISOString().slice(0, 16), // ✅ استخدام slice(0, 16) للحصول على التاريخ والوقت", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 129, "content": "date: new Date().toISOString().slice(0, 16), // ✅ استخدام slice(0, 16) للحصول على التاريخ والوقت", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "STRING_DATE_TYPE", "line": 239, "content": "const formatDateTime = (dateTimeString: string): string => {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "MANUAL_DATE_FORMAT", "line": 247, "content": "const year = date.getFullYear();", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "MANUAL_DATE_FORMAT", "line": 248, "content": "const month = String(date.getMonth() + 1).padStart(2, '0');", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "MANUAL_DATE_FORMAT", "line": 249, "content": "const day = String(date.getDate()).padStart(2, '0');", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "TO_ISO_STRING_USAGE", "line": 267, "content": "createdAt: returnItem.createdAt || new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 267, "content": "createdAt: returnItem.createdAt || new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 428, "content": "timestamp: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 428, "content": "timestamp: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 500, "content": "date: new Date(returnOrder.date).toISOString().slice(0, 16), // ✅ استخدام slice(0, 16) للحصول على التاريخ والوقت", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 532, "content": "uploadedAt: returnOrder.createdAt || new Date().toISOString()", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 532, "content": "uploadedAt: returnOrder.createdAt || new Date().toISOString()", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 706, "content": "return `ضمان منتهي (انتهى: ${formatDateTime(expiryDate.toISOString())})`;", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1026, "content": "date: new Date(formState.date).toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1030, "content": "createdAt: new Date().toISOString(), // ← إضافة الختم الزمني", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1030, "content": "createdAt: new Date().toISOString(), // ← إضافة الختم الزمني", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1041, "content": "createdAt: loadedReturn.createdAt || new Date().toISOString(), // ← الحفاظ على وقت الإنشاء الأصلي", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1041, "content": "createdAt: loadedReturn.createdAt || new Date().toISOString(), // ← الحفاظ على وقت الإنشاء الأصلي", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1395, "content": "value={formState.date || new Date().toISOString().slice(0, 16)}", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1395, "content": "value={formState.date || new Date().toISOString().slice(0, 16)}", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1407, "content": "onClick={() => setFormState(s => ({ ...s, date: new Date().toISOString().slice(0, 16) }))}", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1407, "content": "onClick={() => setFormState(s => ({ ...s, date: new Date().toISOString().slice(0, 16) }))}", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 2324, "content": "doc.text(`تاريخ التقرير: ${formatDateTime(new Date().toISOString())}`, 190, 30, { align: 'right' });", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 2324, "content": "doc.text(`تاريخ التقرير: ${formatDateTime(new Date().toISOString())}`, 190, 30, { align: 'right' });", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}], "app\\(main)\\sales\\page.tsx": [{"type": "TO_ISO_STRING_USAGE", "line": 95, "content": "date: new Date().toISOString().slice(0, 16),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 95, "content": "date: new Date().toISOString().slice(0, 16),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 364, "content": "date: draft.formState?.date || new Date().toISOString().slice(0, 16),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 364, "content": "date: draft.formState?.date || new Date().toISOString().slice(0, 16),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 398, "content": "date: draft.formState?.date || new Date().toISOString().slice(0, 16),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 398, "content": "date: draft.formState?.date || new Date().toISOString().slice(0, 16),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 482, "content": "date: sale.date || new Date().toISOString().slice(0, 16),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 482, "content": "date: sale.date || new Date().toISOString().slice(0, 16),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 514, "content": "uploadedAt: sale.createdAt || new Date().toISOString()", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 514, "content": "uploadedAt: sale.createdAt || new Date().toISOString()", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 827, "content": "timestamp: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 827, "content": "timestamp: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "LOCAL_DATE_FORMAT", "line": 879, "content": "description: `تم تحميل المسودة المحفوظة بتاريخ ${new Date(draftData.timestamp).toLocaleString('en-US')}`,", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "TO_ISO_STRING_USAGE", "line": 928, "content": "createdAt: loadedSale.createdAt || new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 928, "content": "createdAt: loadedSale.createdAt || new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "LOCAL_DATE_FORMAT", "line": 1071, "content": "doc.text(new Date(saleToPrint.date).toLocaleDateString('ar-EG'), 160, 59, {", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 1238, "content": "doc.text(`تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')}`, 200, finalY + 17, { align: 'right' });", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 1995, "content": "{new Date(formState.date).toLocaleDateString('ar-EG')}", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 2260, "content": "{new Date(sale.date).toLocaleDateString('en-US')}", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 2678, "content": "<strong>تاريخ الإنشاء:</strong> {existingDraft ? new Date().toLocaleDateString('ar-EG') : ''}", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "app\\(main)\\settings\\appearance-settings.tsx": [{"type": "TO_ISO_STRING_USAGE", "line": 331, "content": "date: new Date().toISOString()", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 331, "content": "date: new Date().toISOString()", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 335, "content": "expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 345, "content": "date: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 345, "content": "date: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "LOCAL_DATE_FORMAT", "line": 346, "content": "formattedDate: new Date().toLocaleDateString('ar-EG'),", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 561, "content": "<p><strong>تاريخ البيع:</strong> ${deviceInfo.lastSale?.date ? new Date(deviceInfo.lastSale.date).toLocaleDateString('ar-EG') : 'غير محدد'}</p>", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 567, "content": "<p><strong>تاريخ الانتهاء:</strong> ${deviceInfo.warrantyInfo?.expiryDate ? new Date(deviceInfo.warrantyInfo.expiryDate).toLocaleDateString('ar-EG') : 'غير محدد'}</p>", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 592, "content": "`<div class=\"timestamp\">تم إنشاء التقرير في: ${new Date().toLocaleString('ar-EG')}</div>` : ''", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1496, "content": "lastSale: { clientName: 'عميل تجريبي', soNumber: 'SO-001', date: new Date().toISOString() },", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1496, "content": "lastSale: { clientName: 'عميل تجريبي', soNumber: 'SO-001', date: new Date().toISOString() },", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1497, "content": "warrantyInfo: { status: 'ضمان ساري', expiryDate: new Date().toISOString(), remaining: '12 شهراً' }", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1497, "content": "warrantyInfo: { status: 'ضمان ساري', expiryDate: new Date().toISOString(), remaining: '12 شهراً' }", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}], "app\\(main)\\stocktaking\\page.tsx": [{"type": "TO_ISO_STRING_USAGE", "line": 1280, "content": "lastSavedAt: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1280, "content": "lastSavedAt: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "JSON_STRINGIFY_DATE", "line": 1288, "content": "localStorage.setItem('stocktakeDrafts', JSON.stringify(updatedDrafts));", "severity": "LOW", "suggestion": "التأكد من معالجة التواريخ بشكل صحيح"}, {"type": "JSON_STRINGIFY_DATE", "line": 1342, "content": "localStorage.setItem('stocktakeDrafts', JSON.stringify(updatedDrafts));", "severity": "LOW", "suggestion": "التأكد من معالجة التواريخ بشكل صحيح"}, {"type": "TO_ISO_STRING_USAGE", "line": 1361, "content": "completedAt: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1361, "content": "completedAt: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "JSON_STRINGIFY_DATE", "line": 1393, "content": "localStorage.setItem('stocktakeHistory', JSON.stringify(updatedHistory));", "severity": "LOW", "suggestion": "التأكد من معالجة التواريخ بشكل صحيح"}, {"type": "JSON_STRINGIFY_DATE", "line": 1397, "content": "localStorage.setItem('stocktakeDrafts', JSON.stringify(updatedDrafts));", "severity": "LOW", "suggestion": "التأكد من معالجة التواريخ بشكل صحيح"}, {"type": "JSON_STRINGIFY_DATE", "line": 1411, "content": "localStorage.setItem('stocktakeDrafts', JSON.stringify(updatedDrafts));", "severity": "LOW", "suggestion": "التأكد من معالجة التواريخ بشكل صحيح"}, {"type": "JSON_STRINGIFY_DATE", "line": 1416, "content": "localStorage.setItem('stocktakeHistory', JSON.stringify(updatedHistory));", "severity": "LOW", "suggestion": "التأكد من معالجة التواريخ بشكل صحيح"}, {"type": "TO_ISO_STRING_USAGE", "line": 1606, "content": "defaultValue={new Date().toISOString().split('T')[0]}", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1606, "content": "defaultValue={new Date().toISOString().split('T')[0]}", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}], "app\\(main)\\stocktaking\\page_new.tsx": [{"type": "LOCAL_DATE_FORMAT", "line": 329, "content": "new Date(stocktake.createdAt).toLocaleDateString('ar-SA'),", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 356, "content": "'تاريخ الإنشاء': new Date(stocktake.createdAt).toLocaleDateString('ar-SA'),", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 357, "content": "'تاريخ البدء': stocktake.startDate ? new Date(stocktake.startDate).toLocaleDateString('ar-SA') : '-',", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 358, "content": "'تاريخ الانتهاء': stocktake.endDate ? new Date(stocktake.endDate).toLocaleDateString('ar-SA') : '-',", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 576, "content": "<TableCell>{new Date(stocktake.createdAt).toLocaleDateString('ar-SA')}</TableCell>", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 747, "content": "<TableCell>{new Date(item.checkedAt).toLocaleString('ar-SA')}</TableCell>", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "app\\(main)\\supply\\page.tsx": [{"type": "TO_ISO_STRING_USAGE", "line": 110, "content": "supplyDate: new Date().toISOString().slice(0, 16), // YYYY-MM-DDTHH:MM format", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 110, "content": "supplyDate: new Date().toISOString().slice(0, 16), // YYYY-MM-DDTHH:MM format", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "STRING_DATE_TYPE", "line": 216, "content": "const formatDateTime = (dateTimeString: string): string => {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "MANUAL_DATE_FORMAT", "line": 224, "content": "const year = date.getFullYear();", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "MANUAL_DATE_FORMAT", "line": 225, "content": "const month = String(date.getMonth() + 1).padStart(2, '0');", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "MANUAL_DATE_FORMAT", "line": 226, "content": "const day = String(date.getDate()).padStart(2, '0');", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "TO_ISO_STRING_USAGE", "line": 478, "content": "supplyDate: draft.formState?.supplyDate || new Date().toISOString().split('T')[0],", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 478, "content": "supplyDate: draft.formState?.supplyDate || new Date().toISOString().split('T')[0],", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 485, "content": "createdAt: draft.timestamp || new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 485, "content": "createdAt: draft.timestamp || new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 618, "content": ": (order.supplyDate || new Date().toISOString().slice(0, 10)) + 'T00:00', // إذا كان تاريخ فقط، أضف وقت افتراضي", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 618, "content": ": (order.supplyDate || new Date().toISOString().slice(0, 10)) + 'T00:00', // إذا كان تاريخ فقط، أضف وقت افتراضي", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 636, "content": "uploadedAt: order.createdAt || new Date().toISOString()", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 636, "content": "uploadedAt: order.createdAt || new Date().toISOString()", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 654, "content": "supplyDate: new Date().toISOString().slice(0, 16), // تاريخ ووقت اليوم", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 654, "content": "supplyDate: new Date().toISOString().slice(0, 16), // تاريخ ووقت اليوم", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 717, "content": "timestamp: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 717, "content": "timestamp: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "LOCAL_DATE_FORMAT", "line": 812, "content": ").toLocaleString('ar-EG')}`,", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 964, "content": "`تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')}`,", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1280, "content": "createdAt: originalOrder?.createdAt || new Date().toISOString(), // الحفاظ على التاريخ الأصلي", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1280, "content": "createdAt: originalOrder?.createdAt || new Date().toISOString(), // الحفاظ على التاريخ الأصلي", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1883, "content": "value={formState.supplyDate || new Date().toISOString().slice(0, 16)}", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1883, "content": "value={formState.supplyDate || new Date().toISOString().slice(0, 16)}", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "LOCAL_DATE_FORMAT", "line": 3256, "content": "<strong>تاريخ الإنشاء:</strong> {existingDraft?.createdAt ? new Date(existingDraft.createdAt).toLocaleDateString('ar-EG') : ''}", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "app\\(main)\\test-export\\page.tsx": [{"type": "TO_ISO_STRING_USAGE", "line": 23, "content": "date: new Date().toISOString()", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 23, "content": "date: new Date().toISOString()", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 35, "content": "date: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 35, "content": "date: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "LOCAL_DATE_FORMAT", "line": 36, "content": "formattedDate: new Date().toLocaleDateString('ar-EG'),", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "TO_ISO_STRING_USAGE", "line": 42, "content": "date: new Date(Date.now() - 86400000).toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "LOCAL_DATE_FORMAT", "line": 43, "content": "formattedDate: new Date(Date.now() - 86400000).toLocaleDateString('ar-EG'),", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "TO_ISO_STRING_USAGE", "line": 49, "content": "date: new Date(Date.now() - 172800000).toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "LOCAL_DATE_FORMAT", "line": 50, "content": "formattedDate: new Date(Date.now() - 172800000).toLocaleDateString('ar-EG'),", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "TO_ISO_STRING_USAGE", "line": 56, "content": "date: new Date(Date.now() - 259200000).toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "LOCAL_DATE_FORMAT", "line": 57, "content": "formattedDate: new Date(Date.now() - 259200000).toLocaleDateString('ar-EG'),", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 219, "content": "تم إنشاؤه في: {new Date().toLocaleDateString('ar-EG')} - {new Date().toLocaleTimeString('ar-EG')}", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 245, "content": "<div><strong>تاريخ البيع:</strong> {new Date().toLocaleDateString('ar-EG')}</div>", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "app\\(main)\\track\\DeviceAdvancedStats.tsx": [{"type": "STRING_DATE_TYPE", "line": 25, "content": "date: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "MANUAL_DATE_FORMAT", "line": 68, "content": "thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "MANUAL_DATE_FORMAT", "line": 76, "content": "sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "MANUAL_DATE_FORMAT", "line": 77, "content": "thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "app\\(main)\\track\\DeviceDetailsSection.tsx": [{"type": "STRING_DATE_TYPE", "line": 34, "content": "function formatArabicDate(date: Date | string): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "LOCAL_DATE_FORMAT", "line": 36, "content": "return dateObj.toLocaleDateString('ar-EG', {", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "app\\(main)\\track\\DeviceHistoryTimeline.tsx": [{"type": "STRING_DATE_TYPE", "line": 35, "content": "date: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 38, "content": "formattedDate?: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 50, "content": "function formatArabicDate(date: Date | string): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "LOCAL_DATE_FORMAT", "line": 52, "content": "return dateObj.toLocaleDateString('ar-EG', {", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "app\\(main)\\track\\DeviceOperationDetails.tsx": [{"type": "STRING_DATE_TYPE", "line": 13, "content": "date: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "LOCAL_DATE_FORMAT", "line": 144, "content": "displayValue = `${value.toLocaleString()} ج.م`;", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 181, "content": "{new Date(operation.date).toLocaleDateString('ar-SA')}", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "app\\(main)\\track\\DeviceTrackingFilters.tsx": [{"type": "STRING_DATE_TYPE", "line": 27, "content": "date: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 30, "content": "formattedDate?: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 145, "content": "const updateFilter = (key: string, value: any) => {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "app\\(main)\\track\\event-log\\page.tsx": [{"type": "STRING_DATE_TYPE", "line": 64, "content": "date: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "app\\(main)\\track\\page.tsx": [{"type": "STRING_DATE_TYPE", "line": 48, "content": "date: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 52, "content": "formattedDate?: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "TO_ISO_STRING_USAGE", "line": 541, "content": "date: device.createdAt || new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 541, "content": "date: device.createdAt || new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}], "app\\(main)\\track\\simple-page.tsx": [{"type": "STRING_DATE_TYPE", "line": 48, "content": "date: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 52, "content": "formattedDate?: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "app\\(main)\\warehouse-transfer\\page.tsx": [{"type": "TO_ISO_STRING_USAGE", "line": 271, "content": "date: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 271, "content": "date: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}], "app\\api\\archive\\route.ts": [{"type": "MANUAL_DATE_FORMAT", "line": 19, "content": "cutoffDate.setDate(cutoffDate.getDate() - daysOld);", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "app\\api\\database\\backup\\route.ts": [{"type": "TO_ISO_STRING_USAGE", "line": 93, "content": "const timestamp = new Date().toISOString().replace(/[:.]/g, '-');", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 93, "content": "const timestamp = new Date().toISOString().replace(/[:.]/g, '-');", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "LOCAL_DATE_FORMAT", "line": 100, "content": "name: name || `نسخة احتياطية - ${new Date().toLocaleDateString('ar-SA')}`,", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "app\\api\\delivery-orders\\route.ts": [{"type": "TO_ISO_STRING_USAGE", "line": 177, "content": "repairDate: repairDate.toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}], "app\\api\\devices\\route.ts": [{"type": "TO_ISO_STRING_USAGE", "line": 79, "content": "dateAdded: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 79, "content": "dateAdded: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}], "app\\api\\employee-requests\\route.ts": [{"type": "TO_ISO_STRING_USAGE", "line": 83, "content": "requestDate: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 83, "content": "requestDate: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 144, "content": "processedDate: new Date().toISOString()", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 144, "content": "processedDate: new Date().toISOString()", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}], "app\\api\\escalation\\overdue\\route.ts": [{"type": "TO_ISO_STRING_USAGE", "line": 78, "content": "requestDate: request.requestDate.toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}], "app\\api\\evaluations\\route.ts": [{"type": "STRING_DATE_TYPE", "line": 69, "content": "const sanitizeDate = (dateStr: any): string | null => {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "TO_ISO_STRING_USAGE", "line": 76, "content": "return date.toISOString();", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 128, "content": "date: cleanedEvaluation.date || new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 128, "content": "date: cleanedEvaluation.date || new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}], "app\\api\\maintenance-logs\\route.ts": [{"type": "TO_ISO_STRING_USAGE", "line": 73, "content": "repairDate: newLog.repairDate || new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 73, "content": "repairDate: newLog.repairDate || new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}], "app\\api\\maintenance-orders\\route.ts": [{"type": "TO_ISO_STRING_USAGE", "line": 120, "content": "date: newOrder.date || new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 120, "content": "date: newOrder.date || new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "JSON_STRINGIFY_DATE", "line": 234, "content": "items: updatedOrder.items ? JSON.stringify(updatedOrder.items) : existingOrder.items,", "severity": "LOW", "suggestion": "التأكد من معالجة التواريخ بشكل صحيح"}], "app\\api\\maintenance-receipts\\route.ts": [{"type": "TO_ISO_STRING_USAGE", "line": 114, "content": "date: newReceipt.date || new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 114, "content": "date: newReceipt.date || new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "JSON_STRINGIFY_DATE", "line": 212, "content": "items: updatedReceipt.items ? JSON.stringify(updatedReceipt.items) : JSON.stringify([]),", "severity": "LOW", "suggestion": "التأكد من معالجة التواريخ بشكل صحيح"}], "app\\api\\notifications\\check-overdue\\route.ts": [{"type": "STRING_DATE_TYPE", "line": 77, "content": "function getOverdueTimeText(priority: string): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "TO_ISO_STRING_USAGE", "line": 129, "content": "timestamp: new Date().toISOString()", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 129, "content": "timestamp: new Date().toISOString()", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}], "app\\api\\notifications\\route.ts": [{"type": "STRING_DATE_TYPE", "line": 144, "content": "function getOverdueTime(priority: string): string {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "app\\api\\response-templates\\route.ts": [{"type": "TO_ISO_STRING_USAGE", "line": 26, "content": "createdAt: template.createdAt.toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 27, "content": "updatedAt: template.updatedAt.toISOString()", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 67, "content": "createdAt: template.createdAt.toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 68, "content": "updatedAt: template.updatedAt.toISOString()", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}], "app\\api\\response-templates\\suggest\\route.ts": [{"type": "TO_ISO_STRING_USAGE", "line": 42, "content": "createdAt: template.createdAt.toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 43, "content": "updatedAt: template.updatedAt.toISOString()", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}], "app\\api\\returns\\route.ts": [{"type": "STRING_DATE_TYPE", "line": 7, "content": "function safeToISOString(dateValue: any): string | null {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "TO_ISO_STRING_USAGE", "line": 15, "content": "return date.toISOString();", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 129, "content": "date: safeToISOString(returnItem.date) || new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 129, "content": "date: safeToISOString(returnItem.date) || new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "STRING_DATE_TYPE", "line": 336, "content": "date: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 344, "content": "processedDate: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 425, "content": "date: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 433, "content": "processedDate: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 531, "content": "date: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 539, "content": "processedDate: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "app\\api\\sales\\route.ts": [{"type": "JSON_STRINGIFY_DATE", "line": 226, "content": "attachments: updatedSale.attachments ? JSON.stringify(updatedSale.attachments) : existingSale.attachments", "severity": "LOW", "suggestion": "التأكد من معالجة التواريخ بشكل صحيح"}], "app\\api\\search\\quick\\route.ts": [{"type": "TO_ISO_STRING_USAGE", "line": 42, "content": "requestDate: request.requestDate.toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}], "app\\api\\search\\requests\\route.ts": [{"type": "STRING_DATE_TYPE", "line": 9, "content": "dateFrom?: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 10, "content": "dateTo?: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "TO_ISO_STRING_USAGE", "line": 139, "content": "requestDate: request.requestDate.toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}], "app\\api\\supply\\route.ts": [{"type": "TO_ISO_STRING_USAGE", "line": 218, "content": "timestamp: new Date().toISOString()", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 218, "content": "timestamp: new Date().toISOString()", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}], "app\\api\\supply-batch\\route.ts": [{"type": "TO_ISO_STRING_USAGE", "line": 157, "content": "timestamp: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 157, "content": "timestamp: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}], "app\\api\\upload\\route.ts": [{"type": "TO_ISO_STRING_USAGE", "line": 93, "content": "uploadedAt: new Date().toISOString()", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 93, "content": "uploadedAt: new Date().toISOString()", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 176, "content": "uploadedAt: stats.mtime.toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}], "components\\AttachmentsViewer.tsx": [{"type": "STRING_DATE_TYPE", "line": 84, "content": "const formatUploadDate = (dateString: string): string => {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "LOCAL_DATE_FORMAT", "line": 86, "content": "return date.toLocaleDateString('ar-EG') + ' ' + date.toLocaleTimeString('ar-EG');", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "components\\database-management-backup.tsx": [{"type": "STRING_DATE_TYPE", "line": 173, "content": "createdAt: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 174, "content": "updatedAt: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 186, "content": "createdAt: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "LOCAL_DATE_FORMAT", "line": 534, "content": "name: backupForm.name || `نسخة احتياطية - ${new Date().toLocaleDateString('ar-SA')}`,", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 850, "content": "{new Date(backup.createdAt).toLocaleDateString('ar-SA', {", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 1004, "content": "placeholder={`نسخة احتياطية - ${new Date().toLocaleDateString('ar-SA')}`}", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "components\\database-management-new.tsx": [{"type": "STRING_DATE_TYPE", "line": 56, "content": "createdAt: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 57, "content": "updatedAt: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 69, "content": "createdAt: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "LOCAL_DATE_FORMAT", "line": 417, "content": "name: backupForm.name || `نسخة احتياطية - ${new Date().toLocaleDateString('ar-SA')}`,", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 847, "content": "{new Date(backup.createdAt).toLocaleDateString('ar-SA', {", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 1078, "content": "placeholder={`نسخة احتياطية - ${new Date().toLocaleDateString('ar-SA')}`}", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "components\\database-management.tsx": [{"type": "STRING_DATE_TYPE", "line": 57, "content": "createdAt: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 58, "content": "updatedAt: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 70, "content": "createdAt: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "LOCAL_DATE_FORMAT", "line": 400, "content": "name: backupForm.name || `نسخة احتياطية - ${new Date().toLocaleDateString('ar-SA')}`,", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 821, "content": "{new Date(backup.createdAt).toLocaleDateString('ar-SA', {", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 1052, "content": "placeholder={`نسخة احتياطية - ${new Date().toLocaleDateString('ar-SA')}`}", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "components\\DeviceTrackingReport.tsx": [{"type": "STRING_DATE_TYPE", "line": 15, "content": "date: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 29, "content": "date: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 33, "content": "expiryDate: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "components\\DocumentHeader.tsx": [{"type": "LOCAL_DATE_FORMAT", "line": 120, "content": "const arabicDate = currentDate.toLocaleDateString('ar-EG');", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}, {"type": "LOCAL_DATE_FORMAT", "line": 121, "content": "const englishDate = currentDate.toLocaleDateString('en-US');", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "components\\evaluation-cleanup.tsx": [{"type": "STRING_DATE_TYPE", "line": 32, "content": "date: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 33, "content": "createdAt: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 182, "content": "const formatDate = (dateString: string) => {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "LOCAL_DATE_FORMAT", "line": 183, "content": "return new Date(dateString).toLocaleString('ar-EG');", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "components\\ReportPreview.tsx": [{"type": "STRING_DATE_TYPE", "line": 39, "content": "date: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 43, "content": "expiryDate: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 56, "content": "date: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "components\\requests\\AdvancedSearch.tsx": [{"type": "TO_ISO_STRING_USAGE", "line": 354, "content": "onSelect={(date) => updateFilter('dateFrom', date?.toISOString().split('T')[0])}", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 374, "content": "onSelect={(date) => updateFilter('dateTo', date?.toISOString().split('T')[0])}", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}], "components\\requests\\EscalationDashboard.tsx": [{"type": "STRING_DATE_TYPE", "line": 37, "content": "requestDate: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "components\\requests\\RequestConversation.tsx": [{"type": "STRING_DATE_TYPE", "line": 34, "content": "createdAt: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 35, "content": "updatedAt: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "components\\requests\\SmartTemplates.tsx": [{"type": "LOCAL_DATE_FORMAT", "line": 108, "content": "currentDate: new Date().toLocaleDateString('ar-SA'),", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "components\\ui\\chart.tsx": [{"type": "LOCAL_DATE_FORMAT", "line": 243, "content": "{item.value.toLocaleString()}", "severity": "MEDIUM", "suggestion": "استخدام دوال date-utils الموحدة"}], "components\\ui\\print-export-buttons.tsx": [{"type": "STRING_DATE_TYPE", "line": 190, "content": "timelineData?: { events: any[]; title?: string };", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}], "context\\stocktake-store.tsx": [{"type": "STRING_DATE_TYPE", "line": 25, "content": "scheduledDate?: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 59, "content": "scheduledDate?: string;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "TO_ISO_STRING_USAGE", "line": 72, "content": "startTime: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 72, "content": "startTime: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 201, "content": "lastSavedAt: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 201, "content": "lastSavedAt: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 226, "content": "completedAt: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 226, "content": "completedAt: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}], "context\\store.tsx": [{"type": "STRING_DATE_TYPE", "line": 100, "content": "updateDeviceStatus: (deviceId: string, status: DeviceStatus) => void;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 204, "content": "updateStocktakeItem: (stocktakeId: number, deviceId: string, updates: Partial<StocktakeItemV1>) => void;", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "STRING_DATE_TYPE", "line": 548, "content": "const updateDeviceStatus = async (deviceId: string, status: DeviceStatus) => {", "severity": "HIGH", "suggestion": "تحويل إلى Date type"}, {"type": "TO_ISO_STRING_USAGE", "line": 572, "content": "lastUpdated: new Date().toISOString()", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 572, "content": "lastUpdated: new Date().toISOString()", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 682, "content": "dateAdded: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 682, "content": "dateAdded: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 699, "content": "dateAdded: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 699, "content": "dateAdded: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 773, "content": "createdAt: o.createdAt || new Date().toISOString(), // ← الحفاظ على التاريخ الأصلي", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 773, "content": "createdAt: o.createdAt || new Date().toISOString(), // ← الحفاظ على التاريخ الأصلي", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 829, "content": "console.log(`📅 تاريخ الأمر: ${orderDate.toISOString()}`);", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 898, "content": "console.log(`  📊 فحص أمر تقييم ${order.orderNumber || order.id}: ${evalDate.toISOString()}`);", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 918, "content": "console.log(`    ⏰ التاريخ لاحق: ${evalDate > orderDate ? 'نعم' : 'لا'} (${evalDate.toISOString()} > ${orderDate.toISOString()})`);", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 938, "content": "console.log(`   أمر الصيانة: ${orderToDelete.orderNumber} - تاريخ: ${orderDate.toISOString()}`);", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 940, "content": "console.log(`   أمر التقييم: ${evalOrder.orderNumber || evalOrder.id} - تاريخ: ${new Date(evalOrder.date).toISOString()}`);", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1502, "content": "const now = new Date().toISOString();", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1502, "content": "const now = new Date().toISOString();", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1548, "content": "lastModifiedAt: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1548, "content": "lastModifiedAt: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1604, "content": "lastModifiedAt: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1604, "content": "lastModifiedAt: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1624, "content": "lastModifiedAt: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1624, "content": "lastModifiedAt: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1647, "content": "lastModifiedAt: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1647, "content": "lastModifiedAt: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1672, "content": "resolvedAt: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1672, "content": "resolvedAt: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1676, "content": "lastModifiedAt: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1676, "content": "lastModifiedAt: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1694, "content": "? { startedAt: new Date().toISOString() }", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1694, "content": "? { startedAt: new Date().toISOString() }", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1697, "content": "? { completedAt: new Date().toISOString() }", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1697, "content": "? { completedAt: new Date().toISOString() }", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1699, "content": "lastModifiedAt: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1699, "content": "lastModifiedAt: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1720, "content": "reviewedAt: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1720, "content": "reviewedAt: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1723, "content": "lastModifiedAt: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1723, "content": "lastModifiedAt: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1743, "content": "date: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1743, "content": "date: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 1758, "content": "dateAdded: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 1758, "content": "dateAdded: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 2307, "content": "processedDate: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 2307, "content": "processedDate: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 2362, "content": "replacementDate: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 2362, "content": "replacementDate: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 2381, "content": "replacementDate: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 2381, "content": "replacementDate: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 2401, "content": "date: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 2401, "content": "date: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 2571, "content": "dateAdded: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 2571, "content": "dateAdded: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 2660, "content": "dateAdded: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 2660, "content": "dateAdded: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 2732, "content": "console.log(`  📊 فحص أمر تقييم ${evalOrder.orderId || evalOrder.id}: ${evalDate.toISOString()}`);", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 2735, "content": "console.log(`    ❌ تاريخ غير لاحق: ${evalDate.toISOString()} <= ${orderDate.toISOString()}`);", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 2976, "content": "date: order.date || new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 2976, "content": "date: order.date || new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 3214, "content": "acknowledgedDate: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 3214, "content": "acknowledgedDate: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 3467, "content": "requestDate: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 3467, "content": "requestDate: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 3613, "content": "sentDate: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 3613, "content": "sentDate: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 3640, "content": "sentDate: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 3640, "content": "sentDate: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}, {"type": "TO_ISO_STRING_USAGE", "line": 3875, "content": "timestamp: new Date().toISOString(),", "severity": "MEDIUM", "suggestion": "استخدام Date object مباشرة"}, {"type": "NEW_DATE_TO_ISO", "line": 3875, "content": "timestamp: new Date().toISOString(),", "severity": "HIGH", "suggestion": "استخدام new Date() مباشرة"}]}}