import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction } from '@/lib/transaction-utils';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    // استرجاع كل الرسائل الداخلية مع المستقبلين من قاعدة البيانات، مرتبة تنازلياً
    const internalMessages = await prisma.internalMessage.findMany({
      include: {
        recipients: {
          include: {
            user: {
              select: { id: true, name: true, email: true }
            }
          }
        }
      },
      orderBy: { id: 'desc' }
    });

    return NextResponse.json(internalMessages);
  } catch (error) {
    console.error('Failed to fetch internal messages:', error);
    return NextResponse.json({ error: 'Failed to fetch internal messages' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newMessage = await request.json();

    // Basic validation
    if (!newMessage.text || !newMessage.senderName) {
      return NextResponse.json(
        { error: 'Message text and sender name are required' },
        { status: 400 }
    );
    }

    const result = await executeInTransaction(async (tx) => {
      // إنشاء الرسالة الجديدة
      const createdMessage = await tx.internalMessage.create({
        data: {
          threadId: newMessage.threadId || 1,
          senderId: newMessage.senderId || 0,
          senderName: newMessage.senderName,
          recipientId: newMessage.recipientId || 0,
          recipientName: newMessage.recipientName || 'الجميع',
          text: newMessage.text,
          attachmentName: newMessage.attachmentName || null,
          attachmentContent: newMessage.attachmentContent || null,
          attachmentType: newMessage.attachmentType || null,
          attachmentUrl: newMessage.attachmentUrl || null,
          attachmentFileName: newMessage.attachmentFileName || null,
          attachmentSize: newMessage.attachmentSize || null,
          sentDate: new Date(),
          status: 'مرسلة',
          isRead: false,
          parentMessageId: newMessage.parentMessageId || null,
          employeeRequestId: newMessage.employeeRequestId || null,
          resolutionNote: newMessage.resolutionNote || null
        }
      });

      // إنشاء سجلات المستقبلين إذا كانت موجودة
      if (newMessage.recipientIds && Array.isArray(newMessage.recipientIds)) {
        await tx.messageRecipient.createMany({
          data: newMessage.recipientIds.map((userId: number) => ({
            messageId: createdMessage.id,
            userId: userId,
            isRead: false
          })),
          skipDuplicates: true
        });
      }

      // استرجاع الرسالة مع المستقبلين
      const messageWithRecipients = await tx.internalMessage.findUnique({
        where: { id: createdMessage.id },
        include: {
          recipients: {
            include: {
              user: {
                select: { id: true, name: true, email: true }
              }
            }
          }
        }
      });

      return { success: true, message: messageWithRecipients };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to create internal message:', error);
    return NextResponse.json({ error: 'Failed to create internal message' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const updatedMessage = await request.json();

    // Basic validation
    if (!updatedMessage.id) {
      return NextResponse.json(
        { error: 'Message ID is required' },
        { status: 400 }
    );
    }

    const result = await executeInTransaction(async (tx) => {
      // التحقق من وجود الرسالة
      const existingMessage = await tx.internalMessage.findUnique({
        where: { id: updatedMessage.id }
      });

      if (!existingMessage) {
        throw new Error('Internal message not found');
      }

      // تحديث الرسالة
      const updated = await tx.internalMessage.update({
        where: { id: updatedMessage.id },
        data: {
          status: updatedMessage.status || existingMessage.status,
          isRead: updatedMessage.isRead !== undefined ? updatedMessage.isRead : existingMessage.isRead,
          resolutionNote: updatedMessage.resolutionNote || existingMessage.resolutionNote
        }
      });

      return { success: true, message: updated };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to update internal message:', error);
    return NextResponse.json({ error: 'Failed to update internal message' }, { status: 500 });
  }
}
