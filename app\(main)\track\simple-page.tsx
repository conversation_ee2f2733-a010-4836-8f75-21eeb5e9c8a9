'use client';

import { useState, useEffect, useMemo } from 'react';
import { useSearch<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useToast } from '@/hooks/use-toast';
import { apiClient, handleApiResponse } from '@/lib/api-client';
import { formatDateTime } from '@/lib/date-utils';
import './track.css';
import './enhanced-styles.css';
import './enhanced-track-styles.css';
import React from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import {
  Barcode,
  Package,
  Wrench,
  ShoppingCart,
  Undo2,
  ClipboardCheck,
  Shuffle,
  User,
  PackageCheck,
  FileText,
  Replace,
  Printer,
  FileDown,
  AlertCircle,
  CheckCircle,
} from 'lucide-react';
import { addDays, addMonths, addYears, isAfter, format, formatDistanceToNowStrict } from 'date-fns';
import { ar } from 'date-fns/locale';
import ReportPreview from '@/components/ReportPreview';
import './print-styles.css';
import './enhanced-device-tracking.css';

type TimelineEvent = {
  icon: React.ReactNode;
  title: string;
  description: string;
  date: string;
  color: string;
  user?: string;
  details?: any;
  formattedDate?: string;
  id?: string;
  type?: string;
};

export default function TrackPage() {
  const { toast } = useToast();
  const searchParams = useSearchParams();
  const router = useRouter();

  // State variables
  const [isLoading, setIsLoading] = useState(false);
  const [devices, setDevices] = useState<any[]>([]);
  const [sales, setSales] = useState<any[]>([]);
  const [returns, setReturns] = useState<any[]>([]);
  const [supplyOrders, setSupplyOrders] = useState<any[]>([]);
  const [suppliers, setSuppliers] = useState<any[]>([]);
  const [evaluationOrders, setEvaluationOrders] = useState<any[]>([]);
  const [maintenanceHistory, setMaintenanceHistory] = useState<any[]>([]);
  const [maintenanceOrders, setMaintenanceOrders] = useState<any[]>([]);
  const [maintenanceReceiptOrders, setMaintenanceReceiptOrders] = useState<any[]>([]);
  const [warehouseTransfers, setWarehouseTransfers] = useState<any[]>([]);
  const [maintenanceLogs, setMaintenanceLogs] = useState<any[]>([]);
  const [maintenanceReceipts, setMaintenanceReceipts] = useState<any[]>([]);
  const [deliveryOrders, setDeliveryOrders] = useState<any[]>([]);

  const [imei, setImei] = useState('');
  const [searchedImei, setSearchedImei] = useState('');
  const [showReportPreview, setShowReportPreview] = useState(false);
  const [filteredTimelineEvents, setFilteredTimelineEvents] = useState<TimelineEvent[]>([]);

  // API functions
  const fetchAllData = async () => {
    setIsLoading(true);
    try {
      const [
        devicesRes,
        salesRes,
        returnsRes,
        supplyRes,
        suppliersRes,
        evaluationsRes,
        maintenanceRes,
        maintenanceOrdersRes,
        maintenanceReceiptsRes,
        warehouseTransfersRes,
        deliveryOrdersRes
      ] = await Promise.all([
        apiClient.get('/api/devices?view=simple'),
        apiClient.get('/api/sales?view=simple'),
        apiClient.get('/api/returns?view=simple'),
        apiClient.get('/api/supply?view=simple'),
        apiClient.get('/api/suppliers?view=simple'),
        apiClient.get('/api/evaluations?view=simple'),
        apiClient.get('/api/maintenance-logs?view=simple'),
        apiClient.get('/api/maintenance-orders?view=simple'),
        apiClient.get('/api/maintenance-receipts?view=simple'),
        apiClient.get('/api/warehouse-transfers'),
        apiClient.get('/api/delivery-orders?view=simple')
      ]);

      if (devicesRes.ok) setDevices(await handleApiResponse(devicesRes));
      if (salesRes.ok) setSales(await handleApiResponse(salesRes));
      if (returnsRes.ok) setReturns(await handleApiResponse(returnsRes));
      if (supplyRes.ok) setSupplyOrders(await handleApiResponse(supplyRes));
      if (suppliersRes.ok) setSuppliers(await handleApiResponse(suppliersRes));
      if (evaluationsRes.ok) setEvaluationOrders(await handleApiResponse(evaluationsRes));
      if (maintenanceRes.ok) setMaintenanceLogs(await handleApiResponse(maintenanceRes));
      if (maintenanceOrdersRes.ok) setMaintenanceOrders(await handleApiResponse(maintenanceOrdersRes));
      if (maintenanceReceiptsRes.ok) setMaintenanceReceipts(await handleApiResponse(maintenanceReceiptsRes));
      if (warehouseTransfersRes.ok) setWarehouseTransfers(await handleApiResponse(warehouseTransfersRes));
      if (deliveryOrdersRes.ok) setDeliveryOrders(await handleApiResponse(deliveryOrdersRes) as any[]);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast({
        title: 'خطأ في تحميل البيانات',
        description: 'حدث خطأ أثناء تحميل بيانات النظام',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchAllData();
  }, []);

  // Handle URL params
  useEffect(() => {
    const idFromUrl = searchParams.get('id');
    if (idFromUrl) {
      setImei(idFromUrl);
      setSearchedImei(idFromUrl);
    }
  }, [searchParams]);

  const handleSearch = (imei: string) => {
    setSearchedImei(imei);
    const params = new URLSearchParams(searchParams);
    params.set('id', imei);
    router.replace(`/track?${params.toString()}`);
  };

  const fullTimelineEvents = useMemo((): TimelineEvent[] => {
    if (!searchedImei) return [];

    const events: TimelineEvent[] = [];
    const device = (devices as any[]).find((d: any) => d.id === searchedImei);

    console.log('🔍 Building comprehensive timeline for device:', searchedImei);
    console.log('Available data sources:', {
      devices: devices?.length || 0,
      sales: sales?.length || 0,
      returns: returns?.length || 0,
      supplyOrders: supplyOrders?.length || 0,
      evaluationOrders: evaluationOrders?.length || 0,
      maintenanceOrders: maintenanceOrders?.length || 0,
      maintenanceReceiptOrders: maintenanceReceiptOrders?.length || 0,
      maintenanceLogs: maintenanceLogs?.length || 0,
      warehouseTransfers: warehouseTransfers?.length || 0,
      deliveryOrders: deliveryOrders?.length || 0
    });

    // 1. Supply Event - توريد الجهاز
    const relatedSupplyOrders = (supplyOrders as any[]).filter((order: any) => {
      if (!order.items) return false;
      try {
        const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items);
        return items.some((item: any) => 
          item.deviceId === searchedImei || 
          item.id === searchedImei || 
          item.imei === searchedImei
        );
      } catch (error) {
        console.warn('Error parsing supply order items:', error);
        return false;
      }
    });

    console.log('📦 Found supply orders:', relatedSupplyOrders.length);
    relatedSupplyOrders.forEach((order: any) => {
      const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items || '[]');
      const deviceItem = items.find((item: any) => 
        item.deviceId === searchedImei || item.id === searchedImei || item.imei === searchedImei
      );

      if (deviceItem) {
        const supplier = (suppliers as any[]).find((s: any) => s.id === order.supplierId);
        const supplierName = supplier?.name || 'مورد غير معروف';
        
        events.push({
          icon: <Package className="h-5 w-5" />,
          title: 'توريد الجهاز',
          description: `تم توريد الجهاز من المورد "${supplierName}" ضمن أمر التوريد ${order.orderNumber}. ${deviceItem.cost ? `التكلفة: ${deviceItem.cost}` : ''} ${deviceItem.condition ? `الحالة: ${deviceItem.condition}` : ''}`,
          date: order.date || order.createdAt,
          color: 'bg-blue-500/20 text-blue-400',
          user: order.employeeName,
          details: {
            orderNumber: order.orderNumber,
            supplierName: supplierName,
            cost: deviceItem.cost,
            condition: deviceItem.condition,
            notes: order.notes,
            warehouseName: order.warehouseName,
            employeeName: order.employeeName
          }
        });
      }
    });

    // 2. Evaluation Events - فحص وتقييم الجهاز
    const relatedEvaluationOrders = (evaluationOrders as any[]).filter((order: any) => {
      if (!order.items) return false;
      try {
        const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items);
        return items.some((item: any) => item.deviceId === searchedImei);
      } catch (error) {
        console.warn('Error parsing evaluation order items:', error);
        return false;
      }
    });

    console.log('🔍 Found evaluation orders:', relatedEvaluationOrders.length);
    relatedEvaluationOrders.forEach((order: any) => {
      const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items || '[]');
      const deviceItem = items.find((item: any) => item.deviceId === searchedImei);

      if (deviceItem) {
        const evaluationResult = deviceItem.result === 'working' ? 'يعمل بشكل طبيعي' :
                               deviceItem.result === 'defective' ? 'يحتاج صيانة' :
                               deviceItem.result === 'damaged' ? 'تالف' :
                               deviceItem.result || 'غير محدد';

        events.push({
          icon: <ClipboardCheck className="h-5 w-5" />,
          title: 'فحص وتقييم',
          description: `تم فحص الجهاز ضمن أمر التقييم ${order.orderNumber}. النتيجة: ${evaluationResult}. ${deviceItem.notes ? `ملاحظات: ${deviceItem.notes}` : ''}`,
          date: order.date || order.createdAt,
          color: deviceItem.result === 'working' ? 'bg-green-500/20 text-green-400' : 
                deviceItem.result === 'defective' ? 'bg-yellow-500/20 text-yellow-400' :
                'bg-red-500/20 text-red-400',
          user: order.employeeName,
          details: {
            orderNumber: order.orderNumber,
            result: deviceItem.result,
            evaluationResult: evaluationResult,
            condition: deviceItem.condition,
            notes: deviceItem.notes,
            estimatedValue: deviceItem.estimatedValue,
            recommendedAction: deviceItem.recommendedAction
          }
        });
      }
    });

    // 3. Maintenance Orders - أوامر الصيانة
    const relatedMaintenanceOrders = (maintenanceOrders as any[]).filter((order: any) => {
      if (!order.items) return false;
      try {
        const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items);
        return items.some((item: any) => item.deviceId === searchedImei);
      } catch (error) {
        console.warn('Error parsing maintenance order items:', error);
        return false;
      }
    });

    console.log('🔧 Found maintenance orders:', relatedMaintenanceOrders.length);
    relatedMaintenanceOrders.forEach((order: any) => {
      const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items || '[]');
      const deviceItem = items.find((item: any) => item.deviceId === searchedImei);

      if (deviceItem) {
        const maintenanceReason = deviceItem.fault || deviceItem.damageType || deviceItem.issueDescription || 'صيانة عامة';
        
        events.push({
          icon: <Wrench className="h-5 w-5" />,
          title: 'إرسال للصيانة',
          description: `تم إرسال الجهاز للصيانة ضمن أمر ${order.orderNumber}. السبب: ${maintenanceReason}. ${deviceItem.expectedCost ? `التكلفة المتوقعة: ${deviceItem.expectedCost}` : ''}`,
          date: order.date || order.createdAt,
          color: 'bg-orange-500/20 text-orange-400',
          user: order.employeeName,
          details: {
            orderNumber: order.orderNumber,
            fault: deviceItem.fault,
            damageType: deviceItem.damageType,
            issueDescription: deviceItem.issueDescription,
            expectedCost: deviceItem.expectedCost,
            maintenanceEmployee: order.maintenanceEmployeeName,
            notes: deviceItem.notes || order.notes
          }
        });
      }
    });

    // 4. Maintenance Receipt Events - استلام من الصيانة
    const relatedMaintenanceReceipts = [
      ...(maintenanceReceiptOrders || []),
      ...(maintenanceReceipts || [])
    ].filter((order: any) => {
      if (!order.items) return false;
      try {
        const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items);
        return items.some((item: any) => item.deviceId === searchedImei);
      } catch (error) {
        console.warn('Error parsing maintenance receipt items:', error);
        return false;
      }
    });

    console.log('📦 Found maintenance receipts:', relatedMaintenanceReceipts.length);
    relatedMaintenanceReceipts.forEach((order: any) => {
      const items = Array.isArray(order.items) ? order.items : JSON.parse(order.items || '[]');
      const deviceItem = items.find((item: any) => item.deviceId === searchedImei);

      if (deviceItem) {
        const resultText = deviceItem.result === 'Repaired' ? 'تم الإصلاح بنجاح' :
                          deviceItem.result === 'Unrepairable-Defective' ? 'غير قابل للإصلاح - معطل فنياً' :
                          deviceItem.result === 'Unrepairable-Damaged' ? 'غير قابل للإصلاح - تالف فيزيائياً' :
                          deviceItem.result === 'PartiallyRepaired' ? 'تم الإصلاح جزئياً' :
                          deviceItem.result || 'نتيجة غير محددة';

        events.push({
          icon: <PackageCheck className="h-5 w-5" />,
          title: 'استلام من الصيانة',
          description: `تم استلام الجهاز من قسم الصيانة ضمن إيصال ${order.receiptNumber}. النتيجة النهائية: ${resultText}. ${deviceItem.repairCost ? `التكلفة الفعلية: ${deviceItem.repairCost}` : ''}`,
          date: order.date || order.createdAt,
          color: deviceItem.result === 'Repaired' ? 'bg-green-500/20 text-green-400' : 
                deviceItem.result === 'PartiallyRepaired' ? 'bg-yellow-500/20 text-yellow-400' :
                'bg-red-500/20 text-red-400',
          user: order.employeeName,
          details: {
            receiptNumber: order.receiptNumber,
            result: deviceItem.result,
            resultText: resultText,
            repairCost: deviceItem.repairCost,
            repairDescription: deviceItem.repairDescription,
            maintenanceEmployee: order.maintenanceEmployeeName
          }
        });
      }
    });

    // 5. Warehouse Transfer Events - التحويلات المخزنية
    (warehouseTransfers as any[]).forEach((transfer: any) => {
      if (Array.isArray(transfer.items) && transfer.items.some((item: any) => item.deviceId === searchedImei)) {
        events.push({
          icon: <Shuffle className="h-5 w-5" />,
          title: 'تحويل مخزني',
          description: `تم تحويل الجهاز من مخزن "${transfer.fromWarehouseName}" إلى مخزن "${transfer.toWarehouseName}" ضمن أمر التحويل ${transfer.transferNumber}`,
          date: transfer.date,
          color: 'bg-purple-500/20 text-purple-400',
          user: transfer.employeeName,
          details: {
            transferNumber: transfer.transferNumber,
            fromWarehouse: transfer.fromWarehouseName,
            toWarehouse: transfer.toWarehouseName,
            status: transfer.status
          }
        });
      }
    });

    // 6. Sale Event - بيع الجهاز
    const sale = (sales as any[]).find((s: any) =>
      (Array.isArray(s.items) && s.items.some((item: any) => item.deviceId === searchedImei))
    );
    if (sale) {
      const saleItem = sale.items.find((item: any) => item.deviceId === searchedImei);
      const warrantyText = sale.warrantyPeriod === '3d' ? '3 أيام' :
                          sale.warrantyPeriod === '1w' ? 'أسبوع' :
                          sale.warrantyPeriod === '1m' ? 'شهر' :
                          sale.warrantyPeriod === '3m' ? '3 أشهر' :
                          sale.warrantyPeriod === '6m' ? '6 أشهر' :
                          sale.warrantyPeriod === '1y' ? 'سنة' : 'بدون ضمان';

      events.push({
        icon: <ShoppingCart className="h-5 w-5" />,
        title: 'بيع الجهاز',
        description: `تم بيع الجهاز للعميل "${sale.clientName}" ضمن فاتورة ${sale.soNumber}. سعر البيع: ${saleItem?.salePrice || 'غير محدد'} - الضمان: ${warrantyText}`,
        date: sale.date,
        color: 'bg-green-500/20 text-green-400',
        user: sale.employeeName || 'قسم المبيعات',
        details: {
          soNumber: sale.soNumber,
          opNumber: sale.opNumber,
          clientName: sale.clientName,
          salePrice: saleItem?.salePrice,
          warrantyPeriod: sale.warrantyPeriod,
          warrantyText: warrantyText
        }
      });
    }

    // 7. Return and Replacement Events - المرتجعات والاستبدال
    (returns as any[]).forEach((returnOrder: any) => {
      const returnedItem = Array.isArray(returnOrder.items) ? returnOrder.items.find(
        (item: any) => item.deviceId === searchedImei
      ) : null;
      const replacementItem = Array.isArray(returnOrder.items) ? returnOrder.items.find(
        (item: any) => item.replacementDeviceId === searchedImei
      ) : null;

      if (returnedItem) {
        events.push({
          icon: <Undo2 className="h-5 w-5" />,
          title: 'إرجاع الجهاز',
          description: `تم إرجاع هذا الجهاز من العميل "${returnOrder.clientName}" في أمر المرتجع رقم ${returnOrder.roNumber}. سبب الإرجاع: ${returnedItem.returnReason || 'غير محدد'}`,
          date: returnOrder.date,
          color: 'bg-red-500/20 text-red-400',
          user: returnOrder.employeeName || 'قسم المرتجعات',
          details: {
            returnOrderNumber: returnOrder.roNumber,
            clientName: returnOrder.clientName,
            returnReason: returnedItem.returnReason,
            returnType: returnedItem.returnType
          }
        });
      }

      if (replacementItem) {
        events.push({
          icon: <Replace className="h-5 w-5" />,
          title: 'جهاز بديل',
          description: `تم صرف هذا الجهاز كبديل للعميل "${returnOrder.clientName}" في أمر المرتجع رقم ${returnOrder.roNumber}`,
          date: returnOrder.date,
          color: 'bg-blue-500/20 text-blue-400',
          user: returnOrder.employeeName || 'قسم المرتجعات',
          details: {
            returnOrderNumber: returnOrder.roNumber,
            clientName: returnOrder.clientName,
            isReplacementDevice: true
          }
        });
      }
    });

    console.log('📊 Total events aggregated:', events.length);

    return events
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
      .map((event, index) => ({
        ...event,
        id: `${event.date}-${event.title}-${index}`,
        type: event.title.includes('توريد') ? 'supply' :
              event.title.includes('فحص') || event.title.includes('تقييم') ? 'evaluation' :
              event.title.includes('صيانة') || event.title.includes('إصلاح') ? 'maintenance' :
              event.title.includes('تحويل') || event.title.includes('نقل') ? 'transfer' :
              event.title.includes('بيع') || event.title.includes('مُباع') ? 'sale' :
              event.title.includes('إرجاع') || event.title.includes('مرتجع') ? 'return' :
              event.title.includes('بديل') || event.title.includes('استبدال') ? 'replacement' :
              event.title.includes('تسليم') ? 'delivery' : 'general',
        formattedDate: formatDateTime(event.date, { arabic: true }),
      }));
  }, [
    searchedImei,
    devices,
    sales,
    returns,
    supplyOrders,
    suppliers,
    evaluationOrders,
    maintenanceHistory,
    maintenanceOrders,
    maintenanceReceiptOrders,
    maintenanceLogs,
    warehouseTransfers,
    deliveryOrders
  ]);

  // تحديث الأحداث المفلترة عند تغيير الأحداث الكاملة
  React.useEffect(() => {
    setFilteredTimelineEvents(fullTimelineEvents);
  }, [fullTimelineEvents]);

  const device = (devices as any[]).find((d: any) => d.id === searchedImei);

  const handlePrint = async (action: 'print' | 'download') => {
    if (!searchedImei || !device) return;

    const deviceData = {
      model: device.model,
      id: searchedImei,
      status: device.status
    };

    if (action === 'print') {
      window.print();
    } else {
      // تنفيذ تصدير PDF هنا
      console.log('Exporting PDF for:', deviceData);
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* شريط البحث */}
      <Card>
        <CardHeader>
          <CardTitle className="text-right">تتبع الأجهزة</CardTitle>
          <CardDescription className="text-right">
            ابحث عن جهاز باستخدام الرقم التسلسلي (IMEI)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Input
              type="text"
              placeholder="أدخل الرقم التسلسلي (IMEI)"
              value={imei}
              onChange={(e) => setImei(e.target.value)}
              className="text-right"
              dir="ltr"
            />
            <Button 
              onClick={() => handleSearch(imei)}
              disabled={!imei.trim() || isLoading}
            >
              <Barcode className="ml-2 h-4 w-4" />
              بحث
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* عرض النتائج */}
      {searchedImei && device && (
        <div className="space-y-6">
          {/* أزرار الطباعة والتصدير */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowReportPreview(true)}
              title="معاينة وطباعة التقرير"
            >
              <FileText className="h-4 w-4 ml-1" />
              معاينة التقرير
            </Button>
            <Button
              variant="outline" 
              size="sm"
              onClick={() => handlePrint('print')}
              title="طباعة التقرير"
            >
              <Printer className="h-4 w-4 ml-1" />
              طباعة
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePrint('download')}
              title="تصدير ملف PDF"
            >
              <FileDown className="h-4 w-4 ml-1" />
              تصدير PDF
            </Button>
          </div>

          {/* السجل التاريخي */}
          <Card>
            <CardHeader>
              <CardTitle className="text-right flex items-center gap-2">
                <ClipboardCheck className="h-5 w-5" />
                السجل التاريخي الشامل
              </CardTitle>
              <CardDescription className="text-right">
                جميع العمليات التي تمت على هذا الجهاز مرتبة زمنياً
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredTimelineEvents.length > 0 ? (
                  filteredTimelineEvents.map((event, index) => (
                    <div key={index} className="flex gap-4 p-4 bg-gray-50 rounded-lg border-r-4 border-blue-500">
                      <div className="flex-shrink-0">
                        {event.icon}
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="font-semibold text-gray-800">{event.title}</h3>
                          <span className="text-sm text-gray-500">{event.formattedDate}</span>
                        </div>
                        <p className="text-gray-600 mb-2">{event.description}</p>
                        {event.user && (
                          <div className="flex items-center gap-2 text-sm text-gray-500">
                            <User className="h-4 w-4" />
                            <span>{event.user}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <ClipboardCheck className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">لا توجد عمليات مسجلة لهذا الجهاز</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* عرض عندما لا يوجد نتائج */}
      {searchedImei && !device && (
        <Card>
          <CardContent className="text-center py-8">
            <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">لم يتم العثور على جهاز بهذا الرقم التسلسلي</p>
          </CardContent>
        </Card>
      )}

      {/* مكون معاينة التقرير */}
      {searchedImei && device && (
        <ReportPreview
          isOpen={showReportPreview}
          onClose={() => setShowReportPreview(false)}
          deviceData={{
            model: device.model,
            id: searchedImei,
            status: device.status
          }}
          timelineEvents={filteredTimelineEvents.map(event => ({
            id: `${event.date}-${event.title}`,
            type: event.title.includes('بيع') ? 'بيع' :
                  event.title.includes('إرجاع') ? 'إرجاع' :
                  event.title.includes('صيانة') ? 'صيانة' :
                  event.title.includes('تقييم') ? 'تقييم' :
                  event.title.includes('توريد') ? 'توريد' : 'عام',
            title: event.title,
            description: event.description,
            date: event.date,
            user: event.user
          }))}
        />
      )}
    </div>
  );
}
